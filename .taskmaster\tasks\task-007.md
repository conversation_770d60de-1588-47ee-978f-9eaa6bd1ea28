# Task #7: Authentication API Integration

## Overview
**Status:** pending  
**Priority:** high  
**Dependencies:** Task #2, Task #4, Task #6  

## Description
Integrate login and signup functionality with backend authentication APIs

## Implementation Details
Implement API integration for authentication endpoints (/api/auth/login, /api/auth/signup). Configure Axios with proper headers and credential handling. Add form validation, error handling, and success redirects to dashboard.

## Test Strategy
Test successful login/signup redirects to dashboard, invalid credentials show error messages, and authentication tokens are stored correctly

## API Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/signup` - User registration
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/logout` - User logout

## Request/Response Formats

### Login Request
```javascript
{
  email: "<EMAIL>",
  password: "password123"
}
```

### Login Response
```javascript
{
  success: true,
  token: "jwt_token_here",
  user: {
    id: 1,
    email: "<EMAIL>",
    username: "username"
  }
}
```

### Signup Request
```javascript
{
  email: "<EMAIL>",
  password: "password123",
  confirmPassword: "password123",
  username: "username"
}
```

## Key Features
- Axios configuration with interceptors
- Token storage and retrieval
- Automatic token refresh (if supported)
- Request/response error handling
- Loading states during API calls
- Form validation before API calls
- Success/error toast notifications

## Acceptance Criteria
- [ ] Axios is configured with base URL and interceptors
- [ ] Login API integration works correctly
- [ ] Signup API integration works correctly
- [ ] Authentication tokens are stored securely
- [ ] Error handling for API failures
- [ ] Loading states during API calls
- [ ] Form validation before submission
- [ ] Success redirects to dashboard
- [ ] Error messages displayed via toasts
- [ ] Token expiration handling

## Files to Create/Modify
- `src/services/api.js` - Axios configuration
- `src/services/authService.js` - Authentication API calls
- `src/utils/tokenManager.js` - Token storage/retrieval
- Update `src/contexts/AuthContext.jsx` - Integrate API calls
