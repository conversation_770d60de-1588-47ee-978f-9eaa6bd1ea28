# Task #10: Analytics Page with Charts

## Overview
**Status:** pending  
**Priority:** low  
**Dependencies:** Task #9  

## Description
Create analytics page with detailed statistics and charts for individual links

## Implementation Details
Build analytics page displaying detailed statistics for individual shortened URLs. Include total clicks, creation/expiry dates, and interactive charts using Recharts. Integrate with /api/analytics/:shortCode endpoint. Add copy button and optional QR code generation.

## Test Strategy
Test analytics data loads correctly, charts render properly, copy functionality works, and page is responsive across devices

## Analytics Components

### Overview Statistics
- Total clicks
- Unique visitors
- Creation date
- Expiry date (if applicable)
- Current status
- Average clicks per day

### Charts and Visualizations
- **Click Timeline** - Line chart showing clicks over time
- **Geographic Distribution** - Map or bar chart by country
- **Referrer Sources** - Pie chart of traffic sources
- **Device Types** - Bar chart (mobile, desktop, tablet)
- **Browser Distribution** - Pie chart of browsers used

### Additional Features
- Date range selector
- Export data functionality
- QR code generation
- Social sharing buttons
- Link performance comparison

## Chart Library Integration (Recharts)
- Line charts for time-based data
- Bar charts for categorical data
- Pie charts for distribution data
- Responsive chart containers
- Custom tooltips and legends
- Color themes matching app design

## API Integration
- `GET /api/analytics/:shortCode` - Get link analytics
- `GET /api/analytics/:shortCode/timeline` - Get click timeline
- `GET /api/analytics/:shortCode/geo` - Get geographic data
- `GET /api/analytics/:shortCode/referrers` - Get referrer data

## Data Structure Examples

### Analytics Response
```javascript
{
  totalClicks: 1250,
  uniqueVisitors: 890,
  createdAt: "2025-01-01",
  expiryDate: null,
  status: "active",
  timeline: [...],
  geography: [...],
  referrers: [...],
  devices: [...]
}
```

## Acceptance Criteria
- [ ] Analytics page is created and accessible
- [ ] Overview statistics are displayed
- [ ] Interactive charts using Recharts
- [ ] Click timeline chart
- [ ] Geographic distribution visualization
- [ ] Referrer sources chart
- [ ] Device types breakdown
- [ ] Date range filtering
- [ ] QR code generation
- [ ] Copy link functionality
- [ ] Responsive design for all devices
- [ ] Loading states during data fetch
- [ ] Error handling for missing data
- [ ] Export functionality (optional)

## Files to Create
- `src/pages/Analytics.jsx`
- `src/components/analytics/OverviewStats.jsx`
- `src/components/analytics/ClickTimelineChart.jsx`
- `src/components/analytics/GeographyChart.jsx`
- `src/components/analytics/ReferrerChart.jsx`
- `src/components/analytics/DeviceChart.jsx`
- `src/components/QRCodeGenerator.jsx`
- `src/services/analyticsService.js`
- `src/hooks/useAnalytics.js`
