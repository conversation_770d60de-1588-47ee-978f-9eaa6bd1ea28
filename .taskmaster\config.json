{"models": {"main": {"provider": "mcp", "modelId": "mcp-sampling", "maxTokens": 8192, "temperature": 0.2}, "research": {"provider": "mcp", "modelId": "mcp-sampling", "maxTokens": 8192, "temperature": 0.1}, "fallback": {"provider": "mcp", "modelId": "mcp-sampling", "maxTokens": 8192, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "URL Shortener", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "English", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}