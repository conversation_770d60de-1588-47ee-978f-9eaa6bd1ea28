# Task #8: Dashboard with URL Shortening Form

## Overview
**Status:** pending  
**Priority:** high  
**Dependencies:** Task #3, Task #7  

## Description
Create dashboard page with URL shortening form and basic functionality

## Implementation Details
Build the main dashboard page with URL shortening form. Include input for original URL and optional custom short code. Integrate with /api/shorten endpoint and display generated short URL. Add form validation and loading states.

## Test Strategy
Test URL shortening works correctly, form validation prevents invalid inputs, loading states display properly, and generated URLs are shown

## Dashboard Components

### URL Shortening Form
- Original URL input (required)
- Custom short code input (optional)
- Submit button
- Form validation
- Loading state during submission

### Results Display
- Generated short URL
- Copy to clipboard functionality
- Success message
- QR code (optional)

### Dashboard Layout
- Welcome message with user info
- URL shortening form section
- Recent links preview
- Statistics overview (optional)

## Form Validation Rules
- Original URL must be valid URL format
- Custom short code (if provided) must be alphanumeric
- Custom short code length limits (3-20 characters)
- Check for duplicate custom codes

## API Integration
- `POST /api/shorten` endpoint
- Request format: `{ originalUrl, customCode? }`
- Response format: `{ shortUrl, shortCode, originalUrl }`

## Acceptance Criteria
- [ ] Dashboard page is created and accessible
- [ ] URL shortening form is functional
- [ ] Form validation works correctly
- [ ] API integration for URL shortening
- [ ] Generated short URL is displayed
- [ ] Copy to clipboard functionality
- [ ] Loading states during form submission
- [ ] Error handling for API failures
- [ ] Success notifications
- [ ] Responsive design
- [ ] Protected route (requires authentication)

## Files to Create
- `src/pages/Dashboard.jsx`
- `src/components/UrlShortenForm.jsx`
- `src/components/UrlResult.jsx`
- `src/services/urlService.js`
- `src/utils/urlValidation.js`

## Styling Considerations
- Clean, modern design
- Clear visual hierarchy
- Prominent call-to-action button
- Responsive layout for mobile devices
