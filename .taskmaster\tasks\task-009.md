# Task #9: Link Management Table

## Overview
**Status:** pending  
**Priority:** medium  
**Dependencies:** Task #8  

## Description
Implement table component to display and manage user's shortened links

## Implementation Details
Create a responsive table component to display user's shortened links. Show columns for original URL, short code, short URL, status, clicks, and actions. Integrate with /api/links endpoint to fetch user's links. Include basic edit/delete functionality.

## Test Strategy
Test table displays links correctly, data loads from API, responsive design works on mobile, and basic actions function properly

## Table Columns
- **Original URL** - Truncated with tooltip for full URL
- **Short Code** - The custom or generated code
- **Short URL** - Full shortened URL with copy button
- **Status** - Active/Inactive/Expired
- **Clicks** - Number of times clicked
- **Created** - Creation date
- **Actions** - Edit, Delete, Analytics buttons

## Key Features
- Responsive table design
- Pagination for large datasets
- Search/filter functionality
- Sort by columns
- Bulk actions (optional)
- Loading states
- Empty state when no links

## Mobile Responsiveness
- Card layout for mobile devices
- Horizontal scroll for table
- Collapsible rows with details
- Touch-friendly action buttons

## API Integration
- `GET /api/links` - Fetch user's links
- `PUT /api/links/:id` - Update link
- `DELETE /api/links/:id` - Delete link
- `GET /api/links/:id/stats` - Get link statistics

## Actions Implementation

### Edit Link
- Modal or inline editing
- Update original URL or custom code
- Validation before saving
- Success/error feedback

### Delete Link
- Confirmation dialog
- Soft delete (mark as inactive)
- Remove from table after deletion
- Undo functionality (optional)

## Acceptance Criteria
- [ ] Table component displays user's links
- [ ] All required columns are shown
- [ ] Responsive design for mobile devices
- [ ] API integration for fetching links
- [ ] Edit functionality works correctly
- [ ] Delete functionality with confirmation
- [ ] Copy to clipboard for short URLs
- [ ] Loading states during API calls
- [ ] Empty state when no links exist
- [ ] Pagination for large datasets
- [ ] Search/filter functionality
- [ ] Error handling for API failures

## Files to Create
- `src/components/LinkTable.jsx`
- `src/components/LinkRow.jsx`
- `src/components/EditLinkModal.jsx`
- `src/components/DeleteConfirmDialog.jsx`
- `src/hooks/useLinks.js`
- `src/services/linkService.js`
